import React, { useMemo } from 'react';
import { WordInfo } from '../types';
import { getWordsWithPos, shuffleArray } from '../utils';

interface WordSelectionAreaProps {
  shuffledWords: WordInfo[];
  onSelectWord: (word: WordInfo, index: number) => void;
  // 新增：分块显示相关属性
  allChunks?: string[];
  currentChunkIndex?: number;
  difficultyMode?: 'auto' | 'full';
  // 新增：当前块的已选单词数量和目标数量
  currentChunkSelectedCount?: number;
  currentChunkTargetCount?: number;
}

export default function WordSelectionArea({
  shuffledWords,
  onSelectWord,
  allChunks = [],
  currentChunkIndex = 0,
  difficultyMode = 'full',
  currentChunkSelectedCount = 0,
  currentChunkTargetCount = 0
}: WordSelectionAreaProps) {

  // 使用 useMemo 来固定每个块的单词顺序，避免重新渲染时变化
  const fixedChunkWords = useMemo(() => {
    if (difficultyMode === 'auto' && allChunks.length > 1) {
      return allChunks.map(chunk => {
        const chunkWords = getWordsWithPos(chunk);
        return shuffleArray(chunkWords);
      });
    }
    return [];
  }, [allChunks, difficultyMode]);

  // 如果是智能模式且有多个块，使用分块显示
  if (difficultyMode === 'auto' && allChunks.length > 1) {
    console.log('=== WordSelectionArea Debug Info ===');
    console.log('allChunks:', allChunks);
    console.log('currentChunkIndex:', currentChunkIndex);
    console.log('currentChunkSelectedCount:', currentChunkSelectedCount);
    console.log('currentChunkTargetCount:', currentChunkTargetCount);
    console.log('shuffledWords:', shuffledWords);
    console.log('fixedChunkWords:', fixedChunkWords);

    return (
      <div className="w-full mb-4 overflow-hidden">
        <div className="space-y-3">
          {allChunks.map((chunk, chunkIndex) => {
            const shuffledChunkWords = fixedChunkWords[chunkIndex] || [];
            const isCurrentChunk = chunkIndex === currentChunkIndex;
            const isCompletedChunk = chunkIndex < currentChunkIndex;

            console.log(`--- Chunk ${chunkIndex} ("${chunk}") ---`);
            console.log('shuffledChunkWords:', shuffledChunkWords);
            console.log('isCurrentChunk:', isCurrentChunk);
            console.log('isCompletedChunk:', isCompletedChunk);

            return (
              <div key={chunkIndex} className="relative">
                {/* 块的单词 */}
                <div className={`border rounded-lg p-3 ${isCurrentChunk
                  ? 'border-blue-300 bg-blue-50/30 dark:border-blue-800 dark:bg-blue-900/10'
                  : isCompletedChunk
                    ? 'border-green-300 bg-green-50/30 dark:border-green-800 dark:bg-green-900/10'
                    : 'border-gray-200 bg-gray-50/30 dark:border-gray-700 dark:bg-gray-800/10'
                  }`}>
                  <div className="flex flex-wrap justify-start content-start">
                    {shuffledChunkWords.map((item, idx) => {
                      // 找到这个单词在全局 shuffledWords 中的索引
                      // 修复：对于重复单词，使用更智能的匹配策略
                      let globalIndex = -1;

                      // 首先尝试精确匹配（单词+词性）
                      const exactMatches = shuffledWords
                        .map((w, i) => ({ word: w, index: i }))
                        .filter(({ word }) => word.word === item.word && word.pos === item.pos);

                      if (exactMatches.length > 0) {
                        // 如果有多个匹配，选择第一个可用的
                        globalIndex = exactMatches[0].index;
                      } else {
                        // 如果精确匹配失败，尝试只匹配单词
                        const wordMatches = shuffledWords
                          .map((w, i) => ({ word: w, index: i }))
                          .filter(({ word }) => word.word === item.word);

                        if (wordMatches.length > 0) {
                          globalIndex = wordMatches[0].index;
                        }
                      }

                      // 单词可选择的条件：
                      // 1. 在全局 shuffledWords 中存在（即还没有被选择）
                      // 2. 当前块未完成时，只能选择当前块的单词
                      // 3. 当前块已完成时，可以选择任何块的单词
                      const isCurrentChunkComplete = currentChunkSelectedCount >= currentChunkTargetCount;
                      const canSelectFromThisChunk = isCurrentChunk || isCurrentChunkComplete;
                      const isSelectable = globalIndex !== -1 && canSelectFromThisChunk;

                      console.log(`Word "${item.word}" in chunk ${chunkIndex}:`);
                      console.log('  globalIndex:', globalIndex);
                      console.log('  available matches:', shuffledWords.filter(w => w.word === item.word).length);
                      console.log('  shuffledWords length:', shuffledWords.length);
                      console.log('  shuffledWords content:', shuffledWords.map(w => w.word));
                      // console.log('  sentenceWords length:', sentenceWord?.length || 0);
                      console.log('  isCurrentChunkComplete:', isCurrentChunkComplete);
                      console.log('  canSelectFromThisChunk:', canSelectFromThisChunk);
                      console.log('  isSelectable:', isSelectable);

                      return (
                        <span
                          key={`${chunkIndex}-${item.word}-${idx}`}
                          className={`border rounded-lg m-1 px-2 py-1 flex flex-col items-center shadow-sm text-sm transition-all ${isSelectable
                            ? 'border-gray-300 cursor-pointer bg-gray-50 text-gray-800 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700 dark:hover:bg-gray-700'
                            : 'border-gray-200 bg-gray-100 text-gray-500 cursor-not-allowed dark:bg-gray-700 dark:text-gray-400 dark:border-gray-600'
                            }`}
                          onClick={isSelectable ? () => onSelectWord(item, globalIndex) : undefined}
                        >
                          <span className="font-medium">{item.word}</span>
                          <span className="text-xs opacity-75 hidden sm:inline">{item.pos}</span>
                        </span>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // 传统的单一区域显示（完整模式或单块）
  return (
    <div className="w-full mb-4 overflow-hidden">
      <div className="flex flex-wrap justify-start content-start">
        {shuffledWords.map((item, idx) => (
          <span
            key={item.word + '-' + idx}
            className="border border-gray-300 rounded-lg cursor-pointer m-1 px-2 py-1 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700 dark:hover:bg-gray-700 flex flex-col items-center shadow-sm text-sm"
            onClick={() => onSelectWord(item, idx)}
          >
            <span className="font-medium">{item.word}</span>
            <span className="text-xs opacity-75 hidden sm:inline">{item.pos}</span>
          </span>
        ))}
      </div>
    </div>
  );
}