import React from 'react';
import { WordInfo } from '../types';

interface SentenceAreaProps {
  sentenceWords: WordInfo[];
  onRemoveWord: (word: WordInfo, index: number, fromChunkIndex?: number) => void;
  completedChunks?: { [chunkIndex: number]: WordInfo[] };
  currentChunkIndex?: number;
  totalChunks?: number;
}

export default function SentenceArea({
  sentenceWords,
  onRemoveWord,
  completedChunks = {},
  currentChunkIndex = 0,
  totalChunks = 1
}: SentenceAreaProps) {

  const renderChunk = (chunkWords: WordInfo[], chunkIndex: number, isCurrentChunk: boolean) => {
    return (
      <div key={chunkIndex} className="flex flex-wrap items-center">
        {chunkWords.map((item, idx) => (
          <span
            key={`${chunkIndex}-${idx}`}
            className={`border rounded-lg m-1 px-2 py-1 flex flex-col items-center shadow-sm transition-all text-sm cursor-pointer ${isCurrentChunk
              ? "border-blue-300 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-200 dark:border-blue-800 dark:hover:bg-blue-900/50"
              : "border-green-300 bg-green-50 text-green-800 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-200 dark:border-green-800 dark:hover:bg-green-900/50"
              }`}
            onClick={() => onRemoveWord(item, idx, isCurrentChunk ? undefined : chunkIndex)}
          >
            <span className="font-medium">{item.word}</span>
            <span className="text-xs opacity-75 hidden sm:inline">{item.pos}</span>
          </span>
        ))}
        {/* 在块之间添加分隔符 */}
        {chunkIndex < totalChunks - 1 && (
          <span className="mx-1 text-gray-400 dark:text-gray-500">|</span>
        )}
      </div>
    );
  };

  return (
    <div className="sentence min-h-[80px] border border-gray-300 dark:border-gray-600 rounded-lg p-3 mb-4 flex flex-wrap items-center justify-center shadow-sm bg-white dark:bg-gray-800 transition-all">
      {/* 渲染已完成的块 */}
      {Object.keys(completedChunks).map(chunkIndexStr => {
        const chunkIndex = parseInt(chunkIndexStr);
        if (chunkIndex < currentChunkIndex) {
          return renderChunk(completedChunks[chunkIndex], chunkIndex, false);
        }
        return null;
      })}

      {/* 渲染当前正在填写的块 */}
      {sentenceWords.length > 0 ? (
        renderChunk(sentenceWords, currentChunkIndex, true)
      ) : (
        // 如果没有已完成的块，显示提示
        Object.keys(completedChunks).length === 0 && (
          <p className="text-gray-400 dark:text-gray-500 text-center italic text-sm">
            点击下方单词组成句子
          </p>
        )
      )}
    </div>
  );
}