"use client"
import React, { useState, useEffect, useRef, useCallback } from "react";
import { parseLrcToJson } from "./utils";
import { shuffleArray, getWordsWithPos, getWordsWithPosForChunks } from "./utils";
// import { motion, AnimatePresence } from "framer-motion";
import { ProcessedLyric, WordInfo } from "./types";

// Import the new components
import LoadingIndicator from './components/LoadingIndicator';
import ProgressBar from './components/ProgressBar';
import SentenceArea from './components/SentenceArea';
import WordSelectionArea from './components/WordSelectionArea';
import TranslationDisplay from './components/TranslationDisplay';
import ActionButtons from './components/ActionButtons';
import ToastNotification from './components/ToastNotification';
import SettingsPanel from './components/SettingsPanel';

export default function Home() {
  const [lyrics, setLyrics] = useState<ProcessedLyric[]>([]);
  const [currentIdx, setCurrentIdx] = useState<number>(() => {
    if (typeof window !== 'undefined') {
      const savedIdx = localStorage.getItem('currentLyricIndex');
      return savedIdx ? parseInt(savedIdx, 10) : 0;
    }
    return 0;
  });
  const [currentText, setCurrentText] = useState<string>("");
  const [shuffledWords, setShuffledWords] = useState<WordInfo[]>([]);
  const [sentenceWords, setSentenceWords] = useState<WordInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentChunkIndex, setCurrentChunkIndex] = useState<number>(0);
  const [currentChunkInfo, setCurrentChunkInfo] = useState<{
    currentChunk: string;
    totalChunks: number;
    isLastChunk: boolean;
    allChunks: string[];
  } | null>(null);
  // 新增：已完成的块状态
  const [completedChunks, setCompletedChunks] = useState<{
    [chunkIndex: number]: WordInfo[];
  }>({});

  const [showTranslation, setShowTranslation] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      const savedSetting = localStorage.getItem('showTranslation');
      return savedSetting ? JSON.parse(savedSetting) : true;
    }
    return true;
  });

  const [difficultyMode, setDifficultyMode] = useState<'auto' | 'full'>(() => {
    if (typeof window !== 'undefined') {
      const savedMode = localStorage.getItem('difficultyMode');
      return savedMode === 'full' ? 'full' : 'auto';
    }
    return 'auto';
  });
  const [toast, setToast] = useState<{
    content: string;
    type: "hint" | "translation" | "success" | "error" | "info";
    visible: boolean;
  }>({ content: "", type: "info", visible: false });
  const [isSettingsOpen, setIsSettingsOpen] = useState<boolean>(false);

  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const workerRef = useRef<Worker | null>(null);
  const lastShuffledSentenceRef = useRef<string>("");

  // 设置当前块的辅助函数
  const setupCurrentChunk = useCallback((lyric: ProcessedLyric, chunkIndex: number = 0, resetCompleted: boolean = false, currentCompletedChunks?: { [chunkIndex: number]: WordInfo[] }, preserveSentenceWords: boolean = false) => {
    const parts = (difficultyMode === 'auto' && lyric.parts && lyric.parts.length > 0)
      ? lyric.parts
      : [lyric.text];

    const currentChunk = parts[chunkIndex] || parts[0];
    const chunkInfo = {
      currentChunk,
      totalChunks: parts.length,
      isLastChunk: chunkIndex >= parts.length - 1,
      allChunks: parts
    };

    setCurrentChunkInfo(chunkInfo);
    setCurrentText(currentChunk);
    setCurrentChunkIndex(chunkIndex);

    if (resetCompleted) {
      setCompletedChunks({});
    }

    // 使用传入的 completedChunks 或当前的 completedChunks
    const chunksToUse = currentCompletedChunks !== undefined ? currentCompletedChunks : completedChunks;

    // 如果传入了新的 completedChunks，更新状态
    if (currentCompletedChunks !== undefined && !resetCompleted) {
      setCompletedChunks(currentCompletedChunks);
    }

    // 使用新的函数生成带有 chunkIndex 的单词
    const fullSentence = chunkInfo.allChunks.join(' ');
    if (lastShuffledSentenceRef.current !== fullSentence || resetCompleted || currentCompletedChunks !== undefined) {
      const allWordsWithPos = getWordsWithPosForChunks(chunkInfo.allChunks);

      // 计算已使用的单词（精确匹配 word + pos + chunkIndex）
      const usedWords: WordInfo[] = [];
      Object.entries(chunksToUse).forEach(([completedChunkIndex, words]) => {
        words.forEach(word => {
          usedWords.push({
            ...word,
            chunkIndex: parseInt(completedChunkIndex)
          });
        });
      });

      // 过滤出可用的单词
      const availableWords = allWordsWithPos.filter(word => {
        return !usedWords.some(used =>
          used.word === word.word &&
          used.pos === word.pos &&
          used.chunkIndex === word.chunkIndex
        );
      });

      setShuffledWords(shuffleArray(availableWords));
      lastShuffledSentenceRef.current = fullSentence;
    }
    if (!preserveSentenceWords) {
      setSentenceWords([]);
    }
  }, [difficultyMode, completedChunks]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('showTranslation', JSON.stringify(showTranslation));
    }
  }, [showTranslation]);



  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('difficultyMode', difficultyMode);
    }
  }, [difficultyMode]);

  useEffect(() => {
    workerRef.current = new Worker(new URL('./worker.ts', import.meta.url));

    workerRef.current.onmessage = (event: MessageEvent<{ type: string; id: number; parts?: string[]; translation?: string; error?: string }>) => {
      const { type, id, parts, translation } = event.data;
      if (type === 'processedResult') {
        setLyrics(prevLyrics => {
          const newLyrics = [...prevLyrics];
          const lyricIndex = newLyrics.findIndex(lyric => lyric.start === id);
          if (lyricIndex !== -1) {
            const updatedLyric = {
              ...newLyrics[lyricIndex],
              parts: parts,
              translation: translation,
              isProcessing: false,
            };
            newLyrics[lyricIndex] = updatedLyric;

            // 如果更新的是当前正在显示的句子，重新设置显示
            if (lyricIndex === currentIdx && difficultyMode === 'auto') {
              setupCurrentChunk(updatedLyric, 0, true);
            }

            if (typeof window !== 'undefined') {
              localStorage.setItem('processedLyrics', JSON.stringify(newLyrics));
            }
          }
          return newLyrics;
        });
      }
    };

    workerRef.current.onerror = (error) => {
      console.error("Worker error:", error);
    };

    const loadLyrics = async () => {
      setLoading(true);
      if (typeof window !== 'undefined') {
        const savedLyrics = localStorage.getItem('processedLyrics');
        if (savedLyrics) {
          const parsedLyrics: ProcessedLyric[] = JSON.parse(savedLyrics);
          setLyrics(parsedLyrics);
          const initialSentence = parsedLyrics[currentIdx] || parsedLyrics[0];
          if (initialSentence) {
            setupCurrentChunk(initialSentence, 0, true);
            if (!parsedLyrics[currentIdx]) setCurrentIdx(0);
          }
          setLoading(false);
          return;
        }
      }

      const res = await fetch("/lyrics/18－Electric Currents in Modern Art.lrc");
      const lrc = await res.text();
      const json = parseLrcToJson(lrc);

      if (json.length > 0) {
        const initialLyrics: ProcessedLyric[] = json.map(lyric => ({
          ...lyric,
          isProcessing: true,
        }));
        setLyrics(initialLyrics);

        initialLyrics.forEach((lyric) => {
          workerRef.current?.postMessage({
            type: 'processSentence',
            id: lyric.start,
            text: lyric.text,
          });
        });

        const initialSentence = initialLyrics[currentIdx] || initialLyrics[0];
        if (initialSentence) {
          setupCurrentChunk(initialSentence, 0, true);
          if (!initialLyrics[currentIdx]) setCurrentIdx(0);
        }
      }
      setLoading(false);
    }

    loadLyrics();

    return () => {
      workerRef.current?.terminate();
    };
  }, [currentIdx, difficultyMode, setupCurrentChunk]); // Add setupCurrentChunk to dependencies

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('currentLyricIndex', currentIdx.toString());
    }

    if (lyrics.length > 0 && currentIdx < lyrics.length) {
      const nextSentence = lyrics[currentIdx];
      setupCurrentChunk(nextSentence, 0, true); // 重置到第一个块，清空已完成的块
      setCurrentChunkIndex(0);
    } else if (currentIdx >= lyrics.length && lyrics.length > 0) {
      setShuffledWords([]);
      setSentenceWords([]);
      setCurrentChunkInfo(null);
      setCurrentChunkIndex(0);
      setCompletedChunks({});
      if (typeof window !== 'undefined') {
        localStorage.removeItem('currentLyricIndex');
        localStorage.removeItem('processedLyrics');
      }
    }
  }, [currentIdx, lyrics, setupCurrentChunk]);

  const showToast = (content: string, type: "hint" | "translation" | "success" | "error" | "info") => {
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }

    setToast({
      content,
      type,
      visible: true
    });

    hideTimerRef.current = setTimeout(() => {
      setToast(prev => ({ ...prev, visible: false }));
      hideTimerRef.current = null;
    }, 3000);
  };

  const checkSentence = () => {
    // 检查是否所有块都已完成
    if (currentChunkInfo) {
      const allCompletedWords: string[] = [];

      // 收集所有已完成块的单词
      for (let i = 0; i < currentChunkInfo.totalChunks; i++) {
        if (i < currentChunkIndex) {
          // 已完成的块
          const chunkWords = completedChunks[i]?.map(w => w.word) || [];
          allCompletedWords.push(...chunkWords);
        } else if (i === currentChunkIndex) {
          // 当前块
          const currentWords = sentenceWords.map(w => w.word);
          allCompletedWords.push(...currentWords);
        }
      }

      // 检查是否所有块都已正确完成
      const { completed: allChunksCompleted, missingChunkIndex } = checkAllChunksCompleted();

      if (!allChunksCompleted) {
        showToast(`请完成第${missingChunkIndex + 1}部分后再确认`, "info");
        return;
      }

      // 验证完整句子
      const formedSentence = allCompletedWords.join(' ');
      const originalSentence = currentChunkInfo.allChunks.join(' ');

      if (formedSentence === originalSentence) {
        showToast("句子完成！", "success");
        setCurrentIdx(currentIdx + 1);
        setCurrentChunkIndex(0);
      } else {
        showToast("句子不正确，请检查各部分", "error");
        // 清空所有已选择的词语，重新开始
        clearAllSelectedWords();
      }
    } else {
      // 传统单块模式
      const formedSentence = sentenceWords.map(item => item.word).join(' ');
      if (formedSentence === currentText) {
        showToast("正确！", "success");
        setCurrentIdx(currentIdx + 1);
      } else {
        showToast("错误，请重试。", "error");
        setShuffledWords([...shuffledWords, ...sentenceWords]);
        setSentenceWords([]);
      }
    }
  };

  // 新增：检查所有块是否真正完成的辅助函数
  const checkAllChunksCompleted = () => {
    if (!currentChunkInfo) return { completed: false, missingChunkIndex: -1 };

    for (let i = 0; i < currentChunkInfo.totalChunks; i++) {
      const expectedChunkWords = getWordsWithPos(currentChunkInfo.allChunks[i]);
      const expectedWordCount = expectedChunkWords.length;

      if (i < currentChunkIndex) {
        // 检查已完成的块
        const actualWordCount = completedChunks[i]?.length || 0;
        if (actualWordCount !== expectedWordCount) {
          return { completed: false, missingChunkIndex: i };
        }
      } else if (i === currentChunkIndex) {
        // 检查当前块
        const actualWordCount = sentenceWords.length;
        if (actualWordCount !== expectedWordCount) {
          return { completed: false, missingChunkIndex: i };
        }
      } else {
        // 后续块还未开始
        return { completed: false, missingChunkIndex: i };
      }
    }

    return { completed: true, missingChunkIndex: -1 };
  };

  // 新增：清空所有已选择的词语的函数
  const clearAllSelectedWords = () => {
    if (currentChunkInfo) {
      // 重置到第一个块的初始状态，使用 setupCurrentChunk 来正确更新所有相关状态
      setupCurrentChunk(lyrics[currentIdx], 0, true);
      setCompletedChunks({});
    }
  };

  const clickWord = async (wordInfo: WordInfo, idx: number) => {
    const newSentenceWords = [...sentenceWords, wordInfo];
    setSentenceWords(newSentenceWords);
    setShuffledWords(shuffledWords.filter((_, i) => i !== idx));

    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(wordInfo.word);
      window.speechSynthesis.speak(utterance);
    }

    // 检查当前块是否完成
    if (currentChunkInfo && currentChunkInfo.totalChunks > 1) {
      const currentChunkWords = getWordsWithPos(currentText);
      if (newSentenceWords.length === currentChunkWords.length) {
        // 当前块完成，保存并移动到下一块
        setCompletedChunks(prev => ({
          ...prev,
          [currentChunkIndex]: [...newSentenceWords]
        }));

        if (!currentChunkInfo.isLastChunk) {
          // 移动到下一个块
          const nextChunkIndex = currentChunkIndex + 1;
          setupCurrentChunk(lyrics[currentIdx], nextChunkIndex, false);
        } else {
          // 当前是最后一个块，检查是否真的所有块都完成了
          const { completed } = checkAllChunksCompleted();
          if (completed) {
            showToast("所有部分完成，点击确认提交", "success");
          }
        }
      }
    }
  }

  const removeWordFromSentence = (wordInfo: WordInfo, idx: number, fromChunkIndex?: number) => {
    if (fromChunkIndex !== undefined && fromChunkIndex !== currentChunkIndex) {
      // 从已完成的块中移除词语 - 这会触发回退逻辑
      if (fromChunkIndex < currentChunkIndex) {
        // 清除该块及之后所有块的内容
        const newCompletedChunks: { [chunkIndex: number]: WordInfo[] } = {};

        // 只保留该块之前的已完成块
        Object.entries(completedChunks).forEach(([chunkIdx, words]) => {
          if (parseInt(chunkIdx) < fromChunkIndex) {
            newCompletedChunks[parseInt(chunkIdx)] = words;
          }
        });

        // 移除该块中的指定单词
        if (completedChunks[fromChunkIndex]) {
          const chunkWords = [...completedChunks[fromChunkIndex]];
          chunkWords.splice(idx, 1);
          if (chunkWords.length > 0) {
            newCompletedChunks[fromChunkIndex] = chunkWords;
          }
        }

        // 更新状态
        setCompletedChunks(newCompletedChunks);

        // 重新设置到指定的块，这会正确更新 currentChunkInfo
        setupCurrentChunk(lyrics[currentIdx], fromChunkIndex, false, newCompletedChunks);

        // 设置当前块的已选单词
        setSentenceWords(newCompletedChunks[fromChunkIndex] || []);
      }
    } else {
      // 从当前块中移除词语
      const newSentenceWords = sentenceWords.filter((_, i) => i !== idx);
      setSentenceWords(newSentenceWords);
      // 将词语返回到选择区域
      setShuffledWords([...shuffledWords, wordInfo]);
    }
  }



  // 修改翻译按钮功能，改为切换翻译显示状态
  const handleToggleTranslation = () => {
    setShowTranslation(!showTranslation);
  };

  const handleRestart = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('currentLyricIndex');
      localStorage.removeItem('processedLyrics');
    }
    setCurrentIdx(0);
    setSentenceWords([]);
    setShuffledWords([]);
    setCompletedChunks({});
    setCurrentChunkIndex(0);
  };

  // const handleRestartCurrentSentence = () => {
  //   if (lyrics.length > 0 && currentIdx < lyrics.length) {
  //     const currentSentence = lyrics[currentIdx];
  //     setupCurrentChunk(currentSentence, 0, true);
  //     setCurrentChunkIndex(0);
  //     showToast("重新开始当前句子", "info");
  //   }
  // };

  const handleOpenSettings = () => {
    setIsSettingsOpen(true);
  };

  const handleCloseSettings = () => {
    setIsSettingsOpen(false);
  };



  const handleChangeDifficultyMode = (mode: 'auto' | 'full') => {
    setDifficultyMode(mode);
    // 重新计算当前句子的块显示
    if (lyrics.length > 0 && currentIdx < lyrics.length) {
      const currentSentence = lyrics[currentIdx];
      setupCurrentChunk(currentSentence, 0, true); // 重置到第一个块，清空已完成的块
      setCurrentChunkIndex(0);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto px-3 py-4 min-h-screen flex flex-col relative">
      {loading ? (
        <LoadingIndicator />
      ) : (
        <div className="flex-1 flex flex-col pb-[72px]">
          <ProgressBar current={currentIdx} total={lyrics.length} onOpenSettings={handleOpenSettings} />



          <SentenceArea
            sentenceWords={sentenceWords}
            onRemoveWord={removeWordFromSentence}
            completedChunks={completedChunks}
            currentChunkIndex={currentChunkIndex}
            totalChunks={currentChunkInfo?.totalChunks || 1}
          />
          <WordSelectionArea
            shuffledWords={shuffledWords}
            onSelectWord={clickWord}
            allChunks={currentChunkInfo?.allChunks || []}
            currentChunkIndex={currentChunkIndex}
            difficultyMode={difficultyMode}
            currentChunkSelectedCount={sentenceWords.length}
            currentChunkTargetCount={currentText ? getWordsWithPos(currentText).length : 0}
          />
          <TranslationDisplay
            isVisible={showTranslation}
            translation={lyrics[currentIdx]?.translation}
          />

          <ActionButtons
            onCheckSentence={checkSentence}
            onRestart={handleRestart}
            isConfirmDisabled={sentenceWords.length === 0}
          />
        </div>
      )}
      <ToastNotification {...toast} />

      {/* 设置面板 */}
      <SettingsPanel
        isVisible={isSettingsOpen}
        onClose={handleCloseSettings}
        showTranslation={showTranslation}
        onToggleTranslation={handleToggleTranslation}
        difficultyMode={difficultyMode}
        onChangeDifficultyMode={handleChangeDifficultyMode}
      />
    </div>
  );
}
