import { processTextWithLLM } from "./langchainUtils";

// Define the message types
interface ProcessSentenceMessage {
  type: 'processSentence';
  id: number; // To identify the sentence being processed
  text: string;
}

interface ProcessedSentenceResult {
  type: 'processedResult';
  id: number;
  parts?: string[]; // 切分后的部分
  translation?: string; // 中文翻译
  error?: string;
}

// Listen for messages from the main thread
self.onmessage = async (event: MessageEvent<ProcessSentenceMessage>) => {
  const { type, id, text } = event.data;
  if (type === 'processSentence') {
    try {

      const task = `
你是一个英语学习助手，需要帮助学习者将复杂的英语句子分解成易于理解的部分。

任务：
1. 智能切分句子：将给定的英语句子按照语法和语义逻辑切分成多个有意义的部分
2. 提供中文翻译：给出整个句子的准确中文翻译

切分规则：
- 每个部分应该是语法上完整且有意义的语言单位
- 优先按照语法结构切分：主语、谓语、宾语、状语、从句等
- 每个部分建议包含4-8个单词，但语法完整性优先于词数
- 保持句子的逻辑顺序，切分后的部分按顺序组合应该等于原句
- 如果句子较短（10个词以下）或无法合理切分，返回空的parts数组

示例：
输入："The quick brown fox jumps over the lazy dog in the garden."
输出：{
  "parts": ["The quick brown fox", "jumps over", "the lazy dog", "in the garden"],
  "translation": "那只敏捷的棕色狐狸跳过了花园里那只懒惰的狗。"
}

输入："I love you."
输出：{
  "parts": [],
  "translation": "我爱你。"
}

要求：
- 只返回JSON格式，不要任何额外说明
- 确保切分后的部分重新组合等于原句
- 翻译要准确自然
- 如果无法切分，parts返回空数组
        `;
      const llmResult = await processTextWithLLM({
        text: text,
        customTask: task,
      });

      const resobj = JSON.parse(llmResult) as { parts: string[], translation: string };

      const result: ProcessedSentenceResult = {
        type: 'processedResult',
        id: id,
        parts: resobj.parts,
        translation: resobj.translation
      };

      // Post the result back to the main thread
      self.postMessage(result);

    } catch (error) {
      const err = error as Error;
      console.error(`Error processing sentence ${id}:`, err);
      const result: ProcessedSentenceResult = {
        type: 'processedResult',
        id: id,
        error: err.message || 'Unknown error',
      };
      self.postMessage(result);
    }
  }
};