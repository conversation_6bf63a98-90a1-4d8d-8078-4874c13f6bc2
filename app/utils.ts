import nlp from "compromise";
import { WordInfo } from "./types";


export interface Lyric {
  time: number;
  text: string;
}
export interface StructuredLyric {
  start: number;
  text: string;
  end: number;
}
export function parseLrcToJson(lrcContent: string): StructuredLyric[] {
  const lines = lrcContent.split('\n');
  const tempLyrics: Lyric[] = [];
  const lyricRegex = /\[(\d{2}):(\d{2})\.(\d{2,3})\](.*)/;
  let startParsing = false
  for (const line of lines) {
    if (line.includes('Lesson')) {
      startParsing = true
    }
    if (!startParsing) continue
    const match = line.match(lyricRegex);
    if (match) {
      const minutes = parseInt(match[1], 10);
      const seconds = parseInt(match[2], 10);
      const milliseconds = parseInt(match[3], 10); // Can be 2 or 3 digits
      const text = match[4].trim();
      // Convert time to milliseconds
      const timeInMs = (minutes * 60 + seconds) * 1000 + (match[3].length === 2 ? milliseconds * 10 : milliseconds);

      // Only include lines with actual text content
      if (text) {
        tempLyrics.push({
          time: timeInMs,
          text: text,
        });
      }
    }
  }

  // Now calculate end times and format the output
  const structuredLyrics: StructuredLyric[] = [];
  for (let i = 0; i < tempLyrics.length; i++) {
    const currentLyric = tempLyrics[i];
    const nextLyric = tempLyrics[i + 1];

    // The end time of the current lyric is the start time of the next lyric.
    // For the last lyric, we can set the end time to its start time,
    // or add a small duration if needed (though using the next timestamp is standard).
    const endTime = nextLyric ? nextLyric.time : currentLyric.time + 10000;

    structuredLyrics.push({
      start: currentLyric.time,
      text: currentLyric.text,
      end: endTime
    });
  }
  return structuredLyrics
}

export function getWords(sentence: string): string[] {
  return sentence.split(' ')
  // return nlp(sentence).terms().out('array');

}

/**
 * Shuffles the words in a sentence randomly.
 * @param sentence The input sentence string.
 * @returns The sentence with words shuffled.
 */
export function shuffleSentence(sentence: string): string {
  const words = getWords(sentence);

  // Fisher-Yates (Knuth) Shuffle Algorithm
  for (let i = words.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [words[i], words[j]] = [words[j], words[i]]; // Swap elements
  }

  return words.join(' ');
}

export function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

export const getWordsWithPos = (text: string, chunkIndex: number = 0): WordInfo[] => {
  const words = getWords(text);
  const doc = nlp(text);
  return words.map((word, index) => {
    const match = doc.match(word);
    const term = match?.terms()?.document?.[0][index].tags;
    const pos = term ? [...term].join(', ') : 'unknown';
    return { word, pos, chunkIndex };
  });
};

// Difficulty grading system functions
export const getDifficultyLevel = (sentenceLength: number): 'easy' | 'medium' | 'hard' => {
  if (sentenceLength <= 8) return 'easy';
  if (sentenceLength <= 15) return 'medium';
  return 'hard';
};

// 智能句子切分函数
export const splitSentenceIntoChunks = (text: string, difficultyMode: 'auto' | 'full'): string[] => {
  if (difficultyMode === 'full') {
    return [text]; // 完整模式返回原句
  }

  const words = getWords(text);
  const totalWords = words.length;

  // 如果句子较短，不需要切分
  if (totalWords <= 8) {
    return [text];
  }

  // 使用 NLP 分析句子结构
  const doc = nlp(text);
  const chunks: string[] = [];

  try {
    // 尝试按照语法结构切分
    const clauses = doc.clauses().out('array');

    if (clauses.length > 1) {
      // 如果有多个从句，按从句切分
      return clauses.filter((clause: string) => clause.trim().length > 0);
    }

    // 如果没有明显的从句，按照语义块切分
    const phrases = doc.match('#Determiner? #Adjective* #Noun+').out('array');
    const verbs = doc.match('#Verb+').out('array');
    const preps = doc.match('#Preposition #Determiner? #Adjective* #Noun+').out('array');

    // 构建语义块
    if (phrases.length > 0) chunks.push(...phrases);
    if (verbs.length > 0) chunks.push(...verbs);
    if (preps.length > 0) chunks.push(...preps);

    // 如果语义分析失败，使用简单的长度切分
    if (chunks.length === 0) {
      return simpleChunkSplit(words);
    }

    // 过滤和清理结果
    const cleanChunks = chunks
      .map(chunk => chunk.trim())
      .filter(chunk => chunk.length > 0)
      .filter((chunk, index, arr) => arr.indexOf(chunk) === index); // 去重

    return cleanChunks.length > 0 ? cleanChunks : [text];

  } catch (error) {
    console.warn('NLP parsing failed, using simple split:', error);
    return simpleChunkSplit(words);
  }
};

// 简单的按长度切分
const simpleChunkSplit = (words: string[]): string[] => {
  const chunks: string[] = [];
  const chunkSize = Math.ceil(words.length / Math.ceil(words.length / 6)); // 每块大约6个词

  for (let i = 0; i < words.length; i += chunkSize) {
    const chunk = words.slice(i, i + chunkSize).join(' ');
    chunks.push(chunk);
  }

  return chunks;
};

// 获取当前块的难度
export const getChunkDifficulty = (chunkText: string): 'easy' | 'medium' | 'hard' => {
  const wordCount = getWords(chunkText).length;
  if (wordCount <= 4) return 'easy';
  if (wordCount <= 7) return 'medium';
  return 'hard';
};

// 处理句子，返回当前应该练习的块
export const getCurrentChunk = (
  originalText: string,
  chunkIndex: number,
  difficultyMode: 'auto' | 'full'
): {
  currentChunk: string;
  totalChunks: number;
  isLastChunk: boolean;
  allChunks: string[];
} => {
  const chunks = splitSentenceIntoChunks(originalText, difficultyMode);
  const currentChunk = chunks[chunkIndex] || chunks[0];

  return {
    currentChunk,
    totalChunks: chunks.length,
    isLastChunk: chunkIndex >= chunks.length - 1,
    allChunks: chunks
  };
};

// 新增一个函数来为每个块的单词添加 chunkIndex
export function getWordsWithPosForChunks(chunks: string[]): WordInfo[] {
  const allWords: WordInfo[] = [];
  let globalIndex = 0;
  
  chunks.forEach((chunk, chunkIndex) => {
    const chunkWords = getWordsWithPos(chunk, chunkIndex);
    chunkWords.forEach(wordInfo => {
      allWords.push({
        ...wordInfo,
        originalIndex: globalIndex++
      });
    });
  });
  
  return allWords;
}